/**
 * 批次3.1节点测试
 * 测试场景编辑节点和材质编辑节点的功能
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { Object3D, MeshStandardMaterial, Vector3, Color } from 'three';

// 场景编辑节点
import {
  SceneViewportNode,
  ObjectSelectionNode,
  ObjectTransformNode,
  ObjectDuplicationNode,
  globalSceneEditingManager
} from '../scene/SceneEditingNodes';

import {
  ObjectGroupingNode,
  ObjectLayerNode,
  GridSnapNode,
  ObjectAlignmentNode,
  ObjectDistributionNode
} from '../scene/SceneEditingNodes2';

import {
  UndoRedoNode,
  HistoryManagementNode,
  SelectionFilterNode,
  ViewportNavigationNode,
  ViewportRenderingNode,
  ViewportSettingsNode
} from '../scene/SceneEditingNodes3';

// 材质编辑节点
import {
  MaterialEditorNode,
  MaterialPreviewNode,
  MaterialLibraryNode,
  globalMaterialEditingManager
} from '../material/MaterialEditingNodes';

import {
  MaterialImportNode,
  MaterialExportNode,
  MaterialValidationNode,
  MaterialOptimizationNode,
  MaterialVersioningNode,
  MaterialSharingNode,
  MaterialAnalyticsNode
} from '../material/MaterialEditingNodes2';

describe('批次3.1节点测试', () => {
  let testObject: Object3D;
  let testMaterial: MeshStandardMaterial;

  beforeEach(() => {
    // 创建测试对象
    testObject = new Object3D();
    testObject.name = 'TestObject';
    testObject.position.set(1, 2, 3);

    // 创建测试材质
    testMaterial = new MeshStandardMaterial({
      name: 'TestMaterial',
      color: 0xff0000,
      metalness: 0.5,
      roughness: 0.3
    });
  });

  describe('场景编辑节点测试', () => {
    test('场景视口节点', () => {
      const node = new SceneViewportNode();
      const result = node.execute({
        setup: true,
        width: 1920,
        height: 1080
      });

      expect(result.onSetup).toBe(true);
      expect(result.viewport).toBeDefined();
      expect(result.viewport.width).toBe(1920);
      expect(result.viewport.height).toBe(1080);
    });

    test('对象选择节点', () => {
      const node = new ObjectSelectionNode();
      const result = node.execute({
        select: true,
        object: testObject,
        addToSelection: false
      });

      expect(result.onSelected).toBe(true);
      expect(result.selectedObjects).toContain(testObject);
      expect(result.selectionCount).toBe(1);
      expect(result.lastSelected).toBe(testObject);
    });

    test('对象变换节点', () => {
      const node = new ObjectTransformNode();
      const newPosition = new Vector3(5, 6, 7);
      
      const result = node.execute({
        transform: true,
        object: testObject,
        position: newPosition,
        relative: false
      });

      expect(result.onTransformed).toBe(true);
      expect(result.transformedObject).toBe(testObject);
      expect(testObject.position.equals(newPosition)).toBe(true);
    });

    test('对象复制节点', () => {
      const node = new ObjectDuplicationNode();
      const result = node.execute({
        duplicate: true,
        objects: [testObject],
        count: 2,
        offset: new Vector3(1, 0, 0)
      });

      expect(result.onDuplicated).toBe(true);
      expect(result.duplicatedObjects.length).toBe(2);
      expect(result.originalObjects).toContain(testObject);
    });

    test('对象分组节点', () => {
      const node = new ObjectGroupingNode();
      const object2 = new Object3D();
      object2.name = 'TestObject2';

      const result = node.execute({
        group: true,
        objects: [testObject, object2],
        groupName: 'TestGroup'
      });

      expect(result.onGrouped).toBe(true);
      expect(result.group).toBeDefined();
      expect(result.group.name).toBe('TestGroup');
      expect(result.groupedObjects.length).toBe(2);
    });

    test('对象图层节点', () => {
      const node = new ObjectLayerNode();
      const result = node.execute({
        setLayer: true,
        object: testObject,
        layer: 2,
        layerName: 'TestLayer',
        visible: true
      });

      expect(result.onSet).toBe(true);
      expect(result.object).toBe(testObject);
      expect(result.layerName).toBe('TestLayer');
      expect(result.isVisible).toBe(true);
    });

    test('网格吸附节点', () => {
      const node = new GridSnapNode();
      testObject.position.set(1.7, 2.3, 3.8);

      const result = node.execute({
        snap: true,
        object: testObject,
        snapSize: 1.0
      });

      expect(result.onSnapped).toBe(true);
      expect(result.snappedObject).toBe(testObject);
      expect(testObject.position.x).toBe(2);
      expect(testObject.position.y).toBe(2);
      expect(testObject.position.z).toBe(4);
    });

    test('对象对齐节点', () => {
      const node = new ObjectAlignmentNode();
      const object2 = new Object3D();
      object2.position.set(5, 6, 7);

      const result = node.execute({
        align: true,
        objects: [testObject, object2],
        alignment: 'center'
      });

      expect(result.onAligned).toBe(true);
      expect(result.alignedObjects.length).toBe(2);
      expect(result.alignmentType).toBe('center');
    });

    test('撤销重做节点', () => {
      const node = new UndoRedoNode();
      
      // 先执行一个操作
      globalSceneEditingManager.addToHistory({
        type: 'transform',
        object: testObject,
        oldPosition: new Vector3(0, 0, 0),
        newPosition: new Vector3(1, 1, 1)
      });

      const result = node.execute({
        undo: true
      });

      expect(result.onUndo).toBe(true);
    });

    test('选择过滤节点', () => {
      const node = new SelectionFilterNode();
      const object2 = new Object3D();
      object2.name = 'AnotherObject';

      const result = node.execute({
        filter: true,
        objects: [testObject, object2],
        filterType: 'name',
        filterValue: 'Test'
      });

      expect(result.onFiltered).toBe(true);
      expect(result.filteredObjects.length).toBe(1);
      expect(result.filteredObjects[0]).toBe(testObject);
      expect(result.excludedObjects.length).toBe(1);
    });
  });

  describe('材质编辑节点测试', () => {
    test('材质编辑器节点', () => {
      const node = new MaterialEditorNode();
      const result = node.execute({
        create: true,
        material: testMaterial,
        materialId: 'test_material'
      });

      expect(result.onCreated).toBe(true);
      expect(result.editor).toBeDefined();
      expect(result.material).toBe(testMaterial);
      expect(result.properties).toBeDefined();
    });

    test('材质预览节点', () => {
      const node = new MaterialPreviewNode();
      const result = node.execute({
        preview: true,
        material: testMaterial,
        geometry: 'sphere',
        environment: 'studio'
      });

      expect(result.onPreviewed).toBe(true);
      expect(result.previewMaterial).toBeDefined();
      expect(result.previewMesh).toBeDefined();
    });

    test('材质库节点', () => {
      const node = new MaterialLibraryNode();
      
      // 添加材质到库
      const addResult = node.execute({
        add: true,
        material: testMaterial,
        materialId: 'test_material'
      });

      expect(addResult.onAdded).toBe(true);

      // 从库获取材质
      const getResult = node.execute({
        get: true,
        materialId: 'test_material'
      });

      expect(getResult.onGot).toBe(true);
    });

    test('材质验证节点', () => {
      const node = new MaterialValidationNode();
      const result = node.execute({
        validate: true,
        material: testMaterial,
        strict: false
      });

      expect(result.onValidated).toBe(true);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    test('材质优化节点', () => {
      const node = new MaterialOptimizationNode();
      const result = node.execute({
        optimize: true,
        material: testMaterial,
        level: 'medium'
      });

      expect(result.onOptimized).toBe(true);
      expect(result.optimizedMaterial).toBeDefined();
      expect(result.originalMaterial).toBe(testMaterial);
      expect(result.optimizationReport).toBeDefined();
    });

    test('材质导出节点', () => {
      const node = new MaterialExportNode();
      
      // 先将材质添加到管理器
      globalMaterialEditingManager.importMaterial({
        id: 'test_material',
        name: 'TestMaterial',
        type: 'MeshStandardMaterial',
        properties: {
          color: 0xff0000,
          metalness: 0.5,
          roughness: 0.3
        }
      });

      const result = node.execute({
        export: true,
        materialId: 'test_material',
        format: 'json'
      });

      expect(result.onExported).toBe(true);
      expect(result.exportData).toBeDefined();
      expect(result.filePath).toContain('test_material.json');
    });

    test('材质分析节点', () => {
      const node = new MaterialAnalyticsNode();
      const result = node.execute({
        analyze: true,
        material: testMaterial,
        includePerformance: true,
        includeUsage: true
      });

      expect(result.onAnalyzed).toBe(true);
      expect(result.analysis).toBeDefined();
      expect(result.performance).toBeDefined();
      expect(result.usage).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });
  });

  describe('集成测试', () => {
    test('场景编辑和材质编辑协同工作', () => {
      // 创建场景视口
      const viewportNode = new SceneViewportNode();
      const viewportResult = viewportNode.execute({
        setup: true,
        width: 800,
        height: 600
      });

      expect(viewportResult.onSetup).toBe(true);

      // 选择对象
      const selectionNode = new ObjectSelectionNode();
      const selectionResult = selectionNode.execute({
        select: true,
        object: testObject
      });

      expect(selectionResult.onSelected).toBe(true);

      // 创建材质编辑器
      const materialEditorNode = new MaterialEditorNode();
      const editorResult = materialEditorNode.execute({
        create: true,
        material: testMaterial
      });

      expect(editorResult.onCreated).toBe(true);

      // 验证材质
      const validationNode = new MaterialValidationNode();
      const validationResult = validationNode.execute({
        validate: true,
        material: testMaterial
      });

      expect(validationResult.isValid).toBe(true);
    });
  });
});
