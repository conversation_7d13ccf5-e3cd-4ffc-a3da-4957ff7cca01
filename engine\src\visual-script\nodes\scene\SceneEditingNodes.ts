/**
 * 场景编辑节点
 * 实现批次3.1的15个场景编辑节点
 */
import { VisualScriptNode } from '../../VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { EventEmitter } from '../../../utils/EventEmitter';
import { Object3D, Vector3, Euler, Quaternion, Box3, Sphere } from 'three';

// 场景编辑管理器
export class SceneEditingManager extends EventEmitter {
  private static instance: SceneEditingManager;
  private viewport: any = null;
  private selectedObjects: Object3D[] = [];
  private transformGizmo: any = null;
  private gridSnap: boolean = false;
  private snapSize: number = 1.0;
  private history: any[] = [];
  private historyIndex: number = -1;
  private maxHistorySize: number = 50;

  public static getInstance(): SceneEditingManager {
    if (!SceneEditingManager.instance) {
      SceneEditingManager.instance = new SceneEditingManager();
    }
    return SceneEditingManager.instance;
  }

  /**
   * 设置视口
   */
  public setViewport(viewport: any): void {
    this.viewport = viewport;
    this.emit('viewportChanged', viewport);
  }

  /**
   * 获取视口
   */
  public getViewport(): any {
    return this.viewport;
  }

  /**
   * 选择对象
   */
  public selectObject(object: Object3D, addToSelection: boolean = false): void {
    if (!addToSelection) {
      this.clearSelection();
    }

    if (!this.selectedObjects.includes(object)) {
      this.selectedObjects.push(object);
      this.emit('objectSelected', { object, selection: this.selectedObjects });
    }
  }

  /**
   * 取消选择对象
   */
  public deselectObject(object: Object3D): void {
    const index = this.selectedObjects.indexOf(object);
    if (index !== -1) {
      this.selectedObjects.splice(index, 1);
      this.emit('objectDeselected', { object, selection: this.selectedObjects });
    }
  }

  /**
   * 清除选择
   */
  public clearSelection(): void {
    const previousSelection = [...this.selectedObjects];
    this.selectedObjects = [];
    this.emit('selectionCleared', { previousSelection });
  }

  /**
   * 获取选中的对象
   */
  public getSelectedObjects(): Object3D[] {
    return [...this.selectedObjects];
  }

  /**
   * 复制对象
   */
  public duplicateObjects(objects: Object3D[]): Object3D[] {
    const duplicated: Object3D[] = [];
    
    objects.forEach(obj => {
      const clone = obj.clone();
      clone.position.add(new Vector3(1, 0, 1)); // 偏移位置
      
      if (obj.parent) {
        obj.parent.add(clone);
      }
      
      duplicated.push(clone);
    });

    this.emit('objectsDuplicated', { original: objects, duplicated });
    return duplicated;
  }

  /**
   * 分组对象
   */
  public groupObjects(objects: Object3D[], groupName: string = 'Group'): Object3D {
    const group = new Object3D();
    group.name = groupName;

    // 计算中心点
    const box = new Box3();
    objects.forEach(obj => box.expandByObject(obj));
    const center = box.getCenter(new Vector3());

    // 设置组的位置为中心点
    group.position.copy(center);

    // 将对象添加到组中，调整相对位置
    objects.forEach(obj => {
      const worldPos = new Vector3();
      obj.getWorldPosition(worldPos);
      
      if (obj.parent) {
        obj.parent.remove(obj);
      }
      
      group.add(obj);
      obj.position.copy(worldPos).sub(center);
    });

    this.emit('objectsGrouped', { group, objects });
    return group;
  }

  /**
   * 设置网格吸附
   */
  public setGridSnap(enabled: boolean, size: number = 1.0): void {
    this.gridSnap = enabled;
    this.snapSize = size;
    this.emit('gridSnapChanged', { enabled, size });
  }

  /**
   * 对齐对象
   */
  public alignObjects(objects: Object3D[], alignment: string): void {
    if (objects.length < 2) return;

    const box = new Box3();
    objects.forEach(obj => box.expandByObject(obj));

    objects.forEach(obj => {
      switch (alignment) {
        case 'left':
          obj.position.x = box.min.x;
          break;
        case 'right':
          obj.position.x = box.max.x;
          break;
        case 'center':
          obj.position.x = (box.min.x + box.max.x) / 2;
          break;
        case 'top':
          obj.position.y = box.max.y;
          break;
        case 'bottom':
          obj.position.y = box.min.y;
          break;
        case 'middle':
          obj.position.y = (box.min.y + box.max.y) / 2;
          break;
      }
    });

    this.emit('objectsAligned', { objects, alignment });
  }

  /**
   * 添加历史记录
   */
  public addToHistory(action: any): void {
    // 移除当前索引之后的历史记录
    this.history = this.history.slice(0, this.historyIndex + 1);
    
    // 添加新的历史记录
    this.history.push(action);
    this.historyIndex++;

    // 限制历史记录大小
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.historyIndex--;
    }

    this.emit('historyChanged', { history: this.history, index: this.historyIndex });
  }

  /**
   * 撤销操作
   */
  public undo(): boolean {
    if (this.historyIndex >= 0) {
      const action = this.history[this.historyIndex];
      this.executeUndoAction(action);
      this.historyIndex--;
      this.emit('undoExecuted', { action, index: this.historyIndex });
      return true;
    }
    return false;
  }

  /**
   * 重做操作
   */
  public redo(): boolean {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      const action = this.history[this.historyIndex];
      this.executeRedoAction(action);
      this.emit('redoExecuted', { action, index: this.historyIndex });
      return true;
    }
    return false;
  }

  private executeUndoAction(action: any): void {
    // 实现撤销逻辑
    switch (action.type) {
      case 'transform':
        action.object.position.copy(action.oldPosition);
        action.object.rotation.copy(action.oldRotation);
        action.object.scale.copy(action.oldScale);
        break;
      case 'delete':
        if (action.parent) {
          action.parent.add(action.object);
        }
        break;
      case 'create':
        if (action.object.parent) {
          action.object.parent.remove(action.object);
        }
        break;
    }
  }

  private executeRedoAction(action: any): void {
    // 实现重做逻辑
    switch (action.type) {
      case 'transform':
        action.object.position.copy(action.newPosition);
        action.object.rotation.copy(action.newRotation);
        action.object.scale.copy(action.newScale);
        break;
      case 'delete':
        if (action.object.parent) {
          action.object.parent.remove(action.object);
        }
        break;
      case 'create':
        if (action.parent) {
          action.parent.add(action.object);
        }
        break;
    }
  }
}

// 全局场景编辑管理器实例
export const globalSceneEditingManager = SceneEditingManager.getInstance();

/**
 * 场景视口节点
 */
export class SceneViewportNode extends VisualScriptNode {
  public static readonly TYPE = 'SceneViewport';
  public static readonly NAME = '场景视口';
  public static readonly DESCRIPTION = '管理场景视口的显示和控制';

  constructor(nodeType: string = SceneViewportNode.TYPE, name: string = SceneViewportNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('setup', 'trigger', '设置视口');
    this.addInput('camera', 'object', '相机');
    this.addInput('renderer', 'object', '渲染器');
    this.addInput('scene', 'object', '场景');
    this.addInput('width', 'number', '宽度');
    this.addInput('height', 'number', '高度');

    // 输出端口
    this.addOutput('viewport', 'object', '视口对象');
    this.addOutput('onSetup', 'trigger', '设置完成');
    this.addOutput('onResize', 'trigger', '尺寸改变');
    this.addOutput('onError', 'trigger', '设置失败');
  }

  public execute(inputs?: any): any {
    try {
      const setupTrigger = inputs?.setup;
      if (!setupTrigger) {
        return this.getDefaultOutputs();
      }

      const camera = inputs?.camera;
      const renderer = inputs?.renderer;
      const scene = inputs?.scene;
      const width = inputs?.width as number || 800;
      const height = inputs?.height as number || 600;

      const viewport = {
        camera,
        renderer,
        scene,
        width,
        height,
        aspect: width / height,
        updateSize: (newWidth: number, newHeight: number) => {
          viewport.width = newWidth;
          viewport.height = newHeight;
          viewport.aspect = newWidth / newHeight;
          if (camera && camera.isPerspectiveCamera) {
            camera.aspect = viewport.aspect;
            camera.updateProjectionMatrix();
          }
          if (renderer) {
            renderer.setSize(newWidth, newHeight);
          }
        }
      };

      globalSceneEditingManager.setViewport(viewport);

      Debug.log('SceneViewportNode', `场景视口设置完成: ${width}x${height}`);

      return {
        viewport,
        onSetup: true,
        onResize: false,
        onError: false
      };

    } catch (error) {
      Debug.error('SceneViewportNode', '场景视口设置失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      viewport: null,
      onSetup: false,
      onResize: false,
      onError: false
    };
  }
}

/**
 * 对象选择节点
 */
export class ObjectSelectionNode extends VisualScriptNode {
  public static readonly TYPE = 'ObjectSelection';
  public static readonly NAME = '对象选择';
  public static readonly DESCRIPTION = '选择和管理场景中的对象';

  constructor(nodeType: string = ObjectSelectionNode.TYPE, name: string = ObjectSelectionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('select', 'trigger', '选择对象');
    this.addInput('deselect', 'trigger', '取消选择');
    this.addInput('clear', 'trigger', '清除选择');
    this.addInput('object', 'object', '目标对象');
    this.addInput('addToSelection', 'boolean', '添加到选择');
    this.addInput('raycast', 'object', '射线检测结果');

    // 输出端口
    this.addOutput('selectedObjects', 'array', '选中的对象');
    this.addOutput('selectionCount', 'number', '选择数量');
    this.addOutput('lastSelected', 'object', '最后选中的对象');
    this.addOutput('onSelected', 'trigger', '选择完成');
    this.addOutput('onDeselected', 'trigger', '取消选择完成');
    this.addOutput('onCleared', 'trigger', '清除完成');
  }

  public execute(inputs?: any): any {
    try {
      const selectTrigger = inputs?.select;
      const deselectTrigger = inputs?.deselect;
      const clearTrigger = inputs?.clear;
      const object = inputs?.object as Object3D;
      const addToSelection = inputs?.addToSelection as boolean || false;
      const raycast = inputs?.raycast;

      let targetObject = object;

      // 如果有射线检测结果，使用检测到的对象
      if (raycast && raycast.object) {
        targetObject = raycast.object;
      }

      if (selectTrigger && targetObject) {
        globalSceneEditingManager.selectObject(targetObject, addToSelection);

        const selectedObjects = globalSceneEditingManager.getSelectedObjects();

        Debug.log('ObjectSelectionNode', `对象选择完成: ${targetObject.name || targetObject.uuid}`);

        return {
          selectedObjects,
          selectionCount: selectedObjects.length,
          lastSelected: targetObject,
          onSelected: true,
          onDeselected: false,
          onCleared: false
        };
      }

      if (deselectTrigger && targetObject) {
        globalSceneEditingManager.deselectObject(targetObject);

        const selectedObjects = globalSceneEditingManager.getSelectedObjects();

        Debug.log('ObjectSelectionNode', `对象取消选择完成: ${targetObject.name || targetObject.uuid}`);

        return {
          selectedObjects,
          selectionCount: selectedObjects.length,
          lastSelected: null,
          onSelected: false,
          onDeselected: true,
          onCleared: false
        };
      }

      if (clearTrigger) {
        globalSceneEditingManager.clearSelection();

        Debug.log('ObjectSelectionNode', '选择清除完成');

        return {
          selectedObjects: [],
          selectionCount: 0,
          lastSelected: null,
          onSelected: false,
          onDeselected: false,
          onCleared: true
        };
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ObjectSelectionNode', '对象选择操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    const selectedObjects = globalSceneEditingManager.getSelectedObjects();
    return {
      selectedObjects,
      selectionCount: selectedObjects.length,
      lastSelected: selectedObjects.length > 0 ? selectedObjects[selectedObjects.length - 1] : null,
      onSelected: false,
      onDeselected: false,
      onCleared: false
    };
  }
}

/**
 * 对象变换节点
 */
export class ObjectTransformNode extends VisualScriptNode {
  public static readonly TYPE = 'ObjectTransform';
  public static readonly NAME = '对象变换';
  public static readonly DESCRIPTION = '变换对象的位置、旋转和缩放';

  constructor(nodeType: string = ObjectTransformNode.TYPE, name: string = ObjectTransformNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('transform', 'trigger', '执行变换');
    this.addInput('object', 'object', '目标对象');
    this.addInput('position', 'vector3', '位置');
    this.addInput('rotation', 'vector3', '旋转');
    this.addInput('scale', 'vector3', '缩放');
    this.addInput('relative', 'boolean', '相对变换');
    this.addInput('recordHistory', 'boolean', '记录历史');

    // 输出端口
    this.addOutput('transformedObject', 'object', '变换后的对象');
    this.addOutput('finalPosition', 'vector3', '最终位置');
    this.addOutput('finalRotation', 'vector3', '最终旋转');
    this.addOutput('finalScale', 'vector3', '最终缩放');
    this.addOutput('onTransformed', 'trigger', '变换完成');
    this.addOutput('onError', 'trigger', '变换失败');
  }

  public execute(inputs?: any): any {
    try {
      const transformTrigger = inputs?.transform;
      if (!transformTrigger) {
        return this.getDefaultOutputs();
      }

      const object = inputs?.object as Object3D;
      if (!object) {
        throw new Error('未提供目标对象');
      }

      const position = inputs?.position as Vector3;
      const rotation = inputs?.rotation as Vector3;
      const scale = inputs?.scale as Vector3;
      const relative = inputs?.relative as boolean || false;
      const recordHistory = inputs?.recordHistory as boolean || true;

      // 记录变换前的状态
      const oldPosition = object.position.clone();
      const oldRotation = object.rotation.clone();
      const oldScale = object.scale.clone();

      // 执行变换
      if (position) {
        if (relative) {
          object.position.add(position);
        } else {
          object.position.copy(position);
        }
      }

      if (rotation) {
        if (relative) {
          object.rotation.x += rotation.x;
          object.rotation.y += rotation.y;
          object.rotation.z += rotation.z;
        } else {
          object.rotation.set(rotation.x, rotation.y, rotation.z);
        }
      }

      if (scale) {
        if (relative) {
          object.scale.multiply(scale);
        } else {
          object.scale.copy(scale);
        }
      }

      // 记录历史
      if (recordHistory) {
        globalSceneEditingManager.addToHistory({
          type: 'transform',
          object,
          oldPosition,
          oldRotation,
          oldScale,
          newPosition: object.position.clone(),
          newRotation: object.rotation.clone(),
          newScale: object.scale.clone()
        });
      }

      Debug.log('ObjectTransformNode', `对象变换完成: ${object.name || object.uuid}`);

      return {
        transformedObject: object,
        finalPosition: object.position.clone(),
        finalRotation: new Vector3(object.rotation.x, object.rotation.y, object.rotation.z),
        finalScale: object.scale.clone(),
        onTransformed: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ObjectTransformNode', '对象变换失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      transformedObject: null,
      finalPosition: new Vector3(),
      finalRotation: new Vector3(),
      finalScale: new Vector3(1, 1, 1),
      onTransformed: false,
      onError: false
    };
  }
}

/**
 * 对象复制节点
 */
export class ObjectDuplicationNode extends VisualScriptNode {
  public static readonly TYPE = 'ObjectDuplication';
  public static readonly NAME = '对象复制';
  public static readonly DESCRIPTION = '复制场景中的对象';

  constructor(nodeType: string = ObjectDuplicationNode.TYPE, name: string = ObjectDuplicationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('duplicate', 'trigger', '执行复制');
    this.addInput('objects', 'array', '要复制的对象');
    this.addInput('count', 'number', '复制数量');
    this.addInput('offset', 'vector3', '偏移量');
    this.addInput('parent', 'object', '父对象');

    // 输出端口
    this.addOutput('duplicatedObjects', 'array', '复制的对象');
    this.addOutput('originalObjects', 'array', '原始对象');
    this.addOutput('totalCount', 'number', '总数量');
    this.addOutput('onDuplicated', 'trigger', '复制完成');
    this.addOutput('onError', 'trigger', '复制失败');
  }

  public execute(inputs?: any): any {
    try {
      const duplicateTrigger = inputs?.duplicate;
      if (!duplicateTrigger) {
        return this.getDefaultOutputs();
      }

      const objects = inputs?.objects as Object3D[] || globalSceneEditingManager.getSelectedObjects();
      const count = Math.max(1, inputs?.count as number || 1);
      const offset = inputs?.offset as Vector3 || new Vector3(1, 0, 1);
      const parent = inputs?.parent as Object3D;

      if (objects.length === 0) {
        throw new Error('没有要复制的对象');
      }

      const allDuplicated: Object3D[] = [];

      for (let i = 0; i < count; i++) {
        const duplicated = globalSceneEditingManager.duplicateObjects(objects);

        // 应用偏移
        duplicated.forEach(obj => {
          obj.position.add(new Vector3().copy(offset).multiplyScalar(i + 1));

          // 设置父对象
          if (parent) {
            parent.add(obj);
          }
        });

        allDuplicated.push(...duplicated);
      }

      Debug.log('ObjectDuplicationNode', `对象复制完成: ${objects.length} 个对象复制 ${count} 次`);

      return {
        duplicatedObjects: allDuplicated,
        originalObjects: objects,
        totalCount: allDuplicated.length,
        onDuplicated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ObjectDuplicationNode', '对象复制失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      duplicatedObjects: [],
      originalObjects: [],
      totalCount: 0,
      onDuplicated: false,
      onError: false
    };
  }
}
